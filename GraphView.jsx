import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { 
  Network, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Settings,
  FileText,
  Tag,
  Hash
} from 'lucide-react'
import '../App.css'

const GraphView = ({ onNodeSelect }) => {
  const [graphData, setGraphData] = useState({ nodes: [], edges: [] })
  const [loading, setLoading] = useState(true)
  const [selectedNode, setSelectedNode] = useState(null)
  const [zoomLevel, setZoomLevel] = useState([50])
  const [showSettings, setShowSettings] = useState(false)
  const svgRef = useRef(null)
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 })

  useEffect(() => {
    fetchGraphData()
    
    // Handle window resize
    const handleResize = () => {
      if (svgRef.current) {
        const rect = svgRef.current.parentElement.getBoundingClientRect()
        setDimensions({ width: rect.width, height: rect.height - 100 })
      }
    }

    window.addEventListener('resize', handleResize)
    handleResize()

    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const fetchGraphData = async () => {
    try {
      const response = await fetch('/api/notes/graph')
      const data = await response.json()
      
      // Process nodes and edges for visualization
      const processedData = processGraphData(data)
      setGraphData(processedData)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching graph data:', error)
      setLoading(false)
    }
  }

  const processGraphData = (data) => {
    const { nodes, edges } = data
    
    // Calculate positions using a simple force-directed layout
    const processedNodes = nodes.map((node, index) => {
      const angle = (index / nodes.length) * 2 * Math.PI
      const radius = Math.min(dimensions.width, dimensions.height) * 0.3
      const centerX = dimensions.width / 2
      const centerY = dimensions.height / 2
      
      return {
        ...node,
        x: centerX + Math.cos(angle) * radius,
        y: centerY + Math.sin(angle) * radius,
        radius: Math.max(20, Math.min(40, node.tags.length * 5 + 15))
      }
    })

    return {
      nodes: processedNodes,
      edges: edges.filter(edge => 
        processedNodes.find(n => n.id === edge.source) && 
        processedNodes.find(n => n.id === edge.target)
      )
    }
  }

  const handleNodeClick = (node) => {
    setSelectedNode(node)
    if (onNodeSelect) {
      onNodeSelect(node)
    }
  }

  const getNodeColor = (node) => {
    if (node.type === 'md') {
      return '#3b82f6' // Blue for markdown
    } else if (node.type === 'json') {
      return '#10b981' // Green for JSON
    }
    return '#6b7280' // Gray default
  }

  const resetView = () => {
    setZoomLevel([50])
    // Reset any transformations
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading graph...</p>
        </div>
      </div>
    )
  }

  if (graphData.nodes.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Network className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">No graph data</h3>
          <p className="text-muted-foreground">
            Import some notes to see the knowledge graph
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Controls */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                <Network className="h-5 w-5" />
                Knowledge Graph
              </CardTitle>
              <CardDescription>
                {graphData.nodes.length} notes, {graphData.edges.length} connections
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2 mr-4">
                <span className="text-sm text-muted-foreground">Zoom:</span>
                <div className="w-24">
                  <Slider
                    value={zoomLevel}
                    onValueChange={setZoomLevel}
                    max={100}
                    min={10}
                    step={5}
                  />
                </div>
              </div>
              <Button variant="outline" size="sm" onClick={resetView}>
                <RotateCcw className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="flex-1 flex gap-4">
        {/* Graph Visualization */}
        <Card className="flex-1">
          <CardContent className="p-0 h-full">
            <div className="relative h-full overflow-hidden">
              <svg
                ref={svgRef}
                width={dimensions.width}
                height={dimensions.height}
                className="border rounded-lg"
                style={{ transform: `scale(${zoomLevel[0] / 50})` }}
              >
                {/* Edges */}
                <g>
                  {graphData.edges.map((edge) => {
                    const sourceNode = graphData.nodes.find(n => n.id === edge.source)
                    const targetNode = graphData.nodes.find(n => n.id === edge.target)
                    
                    if (!sourceNode || !targetNode) return null
                    
                    return (
                      <line
                        key={edge.id}
                        x1={sourceNode.x}
                        y1={sourceNode.y}
                        x2={targetNode.x}
                        y2={targetNode.y}
                        stroke="#e5e7eb"
                        strokeWidth="2"
                        opacity="0.6"
                      />
                    )
                  })}
                </g>

                {/* Nodes */}
                <g>
                  {graphData.nodes.map((node) => (
                    <g key={node.id}>
                      {/* Node circle */}
                      <circle
                        cx={node.x}
                        cy={node.y}
                        r={node.radius}
                        fill={getNodeColor(node)}
                        stroke={selectedNode?.id === node.id ? '#f59e0b' : '#ffffff'}
                        strokeWidth={selectedNode?.id === node.id ? 3 : 2}
                        className="cursor-pointer hover:opacity-80 transition-opacity"
                        onClick={() => handleNodeClick(node)}
                      />
                      
                      {/* Node icon */}
                      <foreignObject
                        x={node.x - 8}
                        y={node.y - 8}
                        width="16"
                        height="16"
                        className="pointer-events-none"
                      >
                        <FileText className="h-4 w-4 text-white" />
                      </foreignObject>
                      
                      {/* Node label */}
                      <text
                        x={node.x}
                        y={node.y + node.radius + 15}
                        textAnchor="middle"
                        className="text-xs fill-current text-gray-700 pointer-events-none"
                        style={{ fontSize: '12px' }}
                      >
                        {node.title.length > 15 ? node.title.substring(0, 15) + '...' : node.title}
                      </text>
                    </g>
                  ))}
                </g>
              </svg>
            </div>
          </CardContent>
        </Card>

        {/* Node Details Panel */}
        {selectedNode && (
          <Card className="w-80">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {selectedNode.title}
              </CardTitle>
              <CardDescription className="flex items-center gap-2">
                <span className="uppercase text-xs font-medium">
                  {selectedNode.type}
                </span>
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Summary */}
              {selectedNode.summary && (
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Hash className="h-4 w-4" />
                    Summary
                  </h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {selectedNode.summary}
                  </p>
                </div>
              )}

              {/* Tags */}
              {selectedNode.tags && selectedNode.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    Tags
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedNode.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Topics */}
              {selectedNode.topics && selectedNode.topics.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Hash className="h-4 w-4" />
                    Topics
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedNode.topics.map((topic, index) => (
                      <Badge 
                        key={index} 
                        variant="outline" 
                        className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                      >
                        {topic}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="pt-2">
                <Button 
                  size="sm" 
                  className="w-full"
                  onClick={() => onNodeSelect && onNodeSelect(selectedNode)}
                >
                  View Note
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Legend */}
      <Card className="mt-4">
        <CardContent className="py-3">
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-blue-500"></div>
              <span>Markdown Notes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-green-500"></div>
              <span>JSON Files</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-1 bg-gray-300"></div>
              <span>Connections</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default GraphView

