---
title: "Python Data Structures"
tags: ["python", "programming", "data-structures", "tutorial"]
created: 2024-01-20
---

# Python Data Structures

Python provides several built-in data structures that are essential for efficient programming. Understanding these structures and when to use them is crucial for writing effective Python code.

## Lists

Lists are ordered, mutable collections that can contain items of different data types.

```python
# Creating a list
fruits = ['apple', 'banana', 'orange']

# Adding items
fruits.append('grape')
fruits.insert(1, 'kiwi')

# Accessing items
first_fruit = fruits[0]
last_fruit = fruits[-1]
```

## Dictionaries

Dictionaries are unordered collections of key-value pairs, perfect for mapping relationships.

```python
# Creating a dictionary
student = {
    'name': '<PERSON>',
    'age': 25,
    'courses': ['Math', 'Science']
}

# Accessing values
name = student['name']
age = student.get('age', 0)
```

## Sets

Sets are unordered collections of unique elements, useful for mathematical operations.

```python
# Creating sets
set1 = {1, 2, 3, 4}
set2 = {3, 4, 5, 6}

# Set operations
union = set1 | set2
intersection = set1 & set2
difference = set1 - set2
```

## Tuples

Tuples are ordered, immutable collections, often used for structured data.

```python
# Creating a tuple
coordinates = (10, 20)
person = ('Alice', 30, 'Engineer')

# Unpacking
x, y = coordinates
name, age, job = person
```

## When to Use Each Structure

- **Lists**: When you need an ordered, mutable collection
- **Dictionaries**: When you need to map keys to values
- **Sets**: When you need unique elements and set operations
- **Tuples**: When you need immutable, ordered data

## Performance Considerations

Different operations have different time complexities:

- List append: O(1)
- List insert at beginning: O(n)
- Dictionary lookup: O(1) average case
- Set membership test: O(1) average case

Understanding these data structures is fundamental to writing efficient Python programs. Choose the right structure based on your specific use case and performance requirements.

