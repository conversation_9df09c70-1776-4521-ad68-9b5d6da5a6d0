            
            # Archive original file
            archived_file = self._archive_file(file_path, content)
            
            # Copy file to notes directory
            filename = os.path.basename(file_path)
            target_path = os.path.join(self.notes_directory, filename)
            
            # Handle filename conflicts
            counter = 1
            base_name, ext = os.path.splitext(filename)
            while os.path.exists(target_path):
                new_filename = f"{base_name}_{counter}{ext}"
                target_path = os.path.join(self.notes_directory, new_filename)
                counter += 1
            
            shutil.copy2(file_path, target_path)
            
            # Process file content based on type
            if file_extension == '.md':
                note = self._process_markdown_file(target_path, content, process_with_llm)
            else:  # .json
                note = self._process_json_file(target_path, content, process_with_llm)
            
            return note
            
        except Exception as e:
            print(f"Error importing file {file_path}: {e}")
            return None
    